import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { MenuItem, Table, OrderItem, Order, TaxRate } from '../types';
import { useMenu } from '../contexts/MenuContext';
import { eventBus, EVENTS } from '../utils/eventBus';
import { calculateTaxes } from '../utils/taxCalculations';
import VirtualizedMenuGrid from './VirtualizedMenuGrid';
import VisualTableSelector from './VisualTableSelector';
import KeyboardShortcutsHelp from './KeyboardShortcutsHelp';
import Icon from './Icon';

interface SimplifiedPOSProps {
  restaurantId: string;
}

type OrderType = 'dine-in' | 'takeaway' | 'delivery';

const SimplifiedPOS: React.FC<SimplifiedPOSProps> = ({ restaurantId }) => {
  // Core state - simplified to essential items only
  const { menuItems, isLoading } = useMenu();
  const [tables, setTables] = useState<Table[]>([]);
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [orderType, setOrderType] = useState<OrderType>('dine-in');
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showTableSelector, setShowTableSelector] = useState(false);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);
  
  // Customer info for delivery orders only
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    address: ''
  });

  // Load tables on component mount
  useEffect(() => {
    loadTables();
  }, [restaurantId]);

  // Debounce search query for better performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Get unique categories from menu items
  const categories = useMemo(() => {
    const cats = ['all', ...new Set(menuItems.map(item => item.category).filter(Boolean))];
    return cats;
  }, [menuItems]);

  // Filter and search menu items with performance optimization
  const filteredMenuItems = useMemo(() => {
    let filtered = menuItems.filter(item => item.available);

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query) ||
        (item.description && item.description.toLowerCase().includes(query)) ||
        (item.code && item.code.toLowerCase().includes(query))
      );
    }

    // Sort by popularity (most ordered first), then by name
    return filtered.sort((a, b) => {
      const popularityDiff = (b.popularity || 0) - (a.popularity || 0);
      if (popularityDiff !== 0) return popularityDiff;
      return a.name.localeCompare(b.name);
    });
  }, [menuItems, selectedCategory, debouncedSearchQuery]);

  // Add item to order with quantity management
  const addToOrder = useCallback((menuItem: MenuItem) => {
    setCurrentOrder(prev => {
      const existingItem = prev.find(item => item.menuItemId === menuItem.id);

      if (existingItem) {
        const updatedOrder = prev.map(item =>
          item.menuItemId === menuItem.id
            ? { ...item, quantity: item.quantity + 1, subtotal: (item.quantity + 1) * menuItem.price }
            : item
        );
        // Emit event for order item added
        eventBus.emit('ORDER_ITEM_ADDED', { menuItem, isUpdate: true });
        return updatedOrder;
      } else {
        const newOrderItem: OrderItem = {
          id: `order-item-${Date.now()}-${Math.random()}`,
          menuItemId: menuItem.id,
          menuItemName: menuItem.name,
          menuItemPrice: menuItem.price,
          quantity: 1,
          subtotal: menuItem.price,
          notes: '',
          status: 'pending'
        };
        // Emit event for order item added
        eventBus.emit('ORDER_ITEM_ADDED', { menuItem, isUpdate: false });
        return [...prev, newOrderItem];
      }
    });
  }, []);

  // Update item quantity
  const updateQuantity = useCallback((itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCurrentOrder(prev => prev.filter(item => item.id !== itemId));
    } else {
      setCurrentOrder(prev =>
        prev.map(item =>
          item.id === itemId
            ? { ...item, quantity: newQuantity, subtotal: newQuantity * item.menuItemPrice }
            : item
        )
      );
    }
  }, []);

  // Clear entire order
  const clearOrder = useCallback(() => {
    setCurrentOrder([]);
    setSelectedTable(null);
    setCustomerInfo({ name: '', phone: '', address: '' });
    // Emit event for order cleared
    eventBus.emit('ORDER_CLEARED');
  }, []);

  // Duplicate last item
  const duplicateLastItem = useCallback(() => {
    if (currentOrder.length === 0) return;

    const lastItem = currentOrder[currentOrder.length - 1];
    const menuItem = menuItems.find(item => item.id === lastItem.menuItemId);
    if (menuItem) {
      addToOrder(menuItem);
    }
  }, [currentOrder, menuItems, addToOrder]);

  // Load tax rates
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);

  useEffect(() => {
    const loadTaxRates = async () => {
      try {
        const data = await window.electronAPI.getTaxRates(restaurantId);
        setTaxRates(data.filter(tax => tax.isActive));
      } catch (error) {
        console.error('Failed to load tax rates:', error);
      }
    };
    loadTaxRates();
  }, [restaurantId]);

  // Calculate order totals using current tax rates
  const orderTotals = useMemo(() => {
    const subtotal = currentOrder.reduce((sum, item) => sum + item.subtotal, 0);
    const taxCalculation = calculateTaxes(subtotal, taxRates);
    return {
      subtotal,
      tax: taxCalculation.taxAmount,
      total: taxCalculation.totalAmount,
      taxBreakdown: taxCalculation.taxBreakdown
    };
  }, [currentOrder, taxRates]);

  // Handle order type change
  const handleOrderTypeChange = useCallback((type: OrderType) => {
    setOrderType(type);
    if (type !== 'dine-in') {
      setSelectedTable(null);
    }
    if (type !== 'delivery') {
      setCustomerInfo({ name: '', phone: '', address: '' });
    }
  }, []);

  // Create order
  const createOrder = useCallback(async () => {
    if (currentOrder.length === 0) {
      alert('Please add items to the order');
      return;
    }

    if (orderType === 'dine-in' && !selectedTable) {
      alert('Please select a table for dine-in orders');
      return;
    }

    if (orderType === 'delivery' && (!customerInfo.name.trim() || !customerInfo.phone.trim() || !customerInfo.address.trim())) {
      alert('Please fill in all customer details for delivery orders');
      return;
    }

    try {
      const orderData = {
        restaurantId,
        orderType,
        tableId: selectedTable?.id || null,
        customerName: orderType === 'delivery' ? customerInfo.name : '',
        customerPhone: orderType === 'delivery' ? customerInfo.phone : '',
        customerAddress: orderType === 'delivery' ? customerInfo.address : '',
        items: currentOrder,
        subtotal: orderTotals.subtotal,
        taxAmount: orderTotals.tax,
        totalAmount: orderTotals.total,
        status: 'pending' as const,
        paymentStatus: 'pending' as const
      };

      const result = await (window.electronAPI as any).createOrder(orderData);

      if (result.success) {
        // Update table status if dine-in
        if (selectedTable && orderType === 'dine-in') {
          await window.electronAPI.updateTable(selectedTable.id, { status: 'occupied' });
          await loadTables();
          eventBus.emit(EVENTS.TABLE_UPDATED);
        }

        // Generate KOT and update order status
        const kotContent = generateKOTContent(result.order);
        alert(`Order ${result.order.orderNumber} created successfully!\n\nKOT Generated:\n${kotContent}`);

        // Update order status to preparing
        await (window.electronAPI as any).updateOrder(result.order.id, {
          kotPrinted: true,
          status: 'preparing'
        });

        // Emit events for dashboard refresh
        eventBus.emit(EVENTS.ORDER_CREATED, result.order);
        eventBus.emit(EVENTS.DASHBOARD_REFRESH);

        clearOrder();

        // Navigate to order management
        eventBus.emit('NAVIGATE_TO_ORDERS');
      } else {
        alert('Failed to create order: ' + result.error);
      }
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Failed to create order');
    }
  }, [currentOrder, orderType, selectedTable, customerInfo, restaurantId, orderTotals, clearOrder]);

  // Generate KOT content
  const generateKOTContent = useCallback((order: Order) => {
    const lines = [
      `Order #${order.orderNumber}`,
      `Type: ${order.orderType.toUpperCase()}`,
      order.tableId ? `Table: ${selectedTable?.tableNumber}` : '',
      `Time: ${new Date().toLocaleTimeString()}`,
      '---',
      ...order.items.map(item => `${item.quantity}x ${item.menuItemName}`),
      '---',
      `Total Items: ${order.items.reduce((sum, item) => sum + item.quantity, 0)}`
    ].filter(Boolean);

    return lines.join('\n');
  }, [selectedTable]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Only handle shortcuts when not typing in inputs
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case 'f':
          // Focus search
          e.preventDefault();
          const searchInput = document.querySelector('.search-input') as HTMLInputElement;
          if (searchInput) {
            searchInput.focus();
          }
          break;
        case 'c':
          // Clear order
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            clearOrder();
          }
          break;
        case 'd':
          // Duplicate last item
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            duplicateLastItem();
          }
          break;
        case 'enter':
          // Create order
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            createOrder();
          }
          break;
        case '1':
        case '2':
        case '3':
          // Quick order type switch
          e.preventDefault();
          const types: OrderType[] = ['dine-in', 'takeaway', 'delivery'];
          const typeIndex = parseInt(e.key) - 1;
          if (typeIndex >= 0 && typeIndex < types.length) {
            handleOrderTypeChange(types[typeIndex]);
          }
          break;
        case 'escape':
          // Clear search or close modals
          e.preventDefault();
          if (showTableSelector) {
            setShowTableSelector(false);
          } else {
            setSearchQuery('');
            setSelectedCategory('all');
          }
          break;
        case 't':
          // Quick table selection for dine-in
          if (orderType === 'dine-in') {
            e.preventDefault();
            setShowTableSelector(true);
          }
          break;
        case 'a':
          // Select all items category
          e.preventDefault();
          setSelectedCategory('all');
          break;
        case '?':
          // Show keyboard shortcuts help
          e.preventDefault();
          setShowKeyboardHelp(true);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [clearOrder, createOrder, handleOrderTypeChange, duplicateLastItem, orderType, showTableSelector]);

  const loadTables = async () => {
    try {
      const data = await window.electronAPI.getTables(restaurantId);
      setTables(data);
    } catch (error) {
      console.error('Failed to load tables:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="simplified-pos-loading">
        <div className="loading-spinner"></div>
        <p>Loading menu items...</p>
      </div>
    );
  }

  return (
    <div className="simplified-pos" data-order-type={orderType}>
      {/* Header with order type selection */}
      <div className="pos-header">
        <div className="order-type-tabs">
          {(['dine-in', 'takeaway', 'delivery'] as OrderType[]).map(type => (
            <button
              key={type}
              className={`order-type-tab ${orderType === type ? 'active' : ''}`}
              onClick={() => handleOrderTypeChange(type)}
            >
              <Icon name={type === 'dine-in' ? 'utensils' : type === 'takeaway' ? 'shopping-cart' : 'delivery'} size="sm" />
              {type === 'dine-in' ? 'Dine-In' : type === 'takeaway' ? 'Takeaway' : 'Delivery'}
            </button>
          ))}
        </div>

        {/* Table selection for dine-in */}
        {orderType === 'dine-in' && (
          <div className="table-selector">
            <button
              className="visual-table-btn"
              onClick={() => setShowTableSelector(true)}
            >
              <Icon name="table" size="sm" />
              {selectedTable ? (
                <span>Table {selectedTable.tableNumber} ({selectedTable.capacity} pax)</span>
              ) : (
                <span>Select Table</span>
              )}
              <Icon name="chevron-right" size="sm" />
            </button>
          </div>
        )}

        {/* Customer info for all order types */}
        {orderType !== 'dine-in' && (
          <div className="customer-info">
            <input
              type="text"
              placeholder={orderType === 'delivery' ? "Customer Name *" : "Customer Name (Optional)"}
              value={customerInfo.name}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
              className="customer-input"
              required={orderType === 'delivery'}
              minLength={orderType === 'delivery' ? 2 : 0}
              maxLength={50}
            />
            <input
              type="tel"
              placeholder={orderType === 'delivery' ? "Phone Number *" : "Phone Number (Optional)"}
              value={customerInfo.phone}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
              className="customer-input"
              required={orderType === 'delivery'}
              pattern={orderType === 'delivery' ? "[0-9]{10,15}" : ""}
              title={orderType === 'delivery' ? "Please enter a valid phone number (10-15 digits)" : ""}
            />
            {orderType === 'delivery' && (
              <input
                type="text"
                placeholder="Delivery Address *"
                value={customerInfo.address}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                className="customer-input"
                required
                minLength={10}
                maxLength={200}
              />
            )}
          </div>
        )}

        {/* Customer info for dine-in (optional) */}
        {orderType === 'dine-in' && (
          <div className="customer-info">
            <input
              type="text"
              placeholder="Customer Name (Optional)"
              value={customerInfo.name}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
              className="customer-input"
              maxLength={50}
            />
            <input
              type="tel"
              placeholder="Phone Number (Optional)"
              value={customerInfo.phone}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
              className="customer-input"
            />
          </div>
        )}

        {/* Help button */}
        <button
          className="help-btn"
          onClick={() => setShowKeyboardHelp(true)}
          title="Keyboard shortcuts (Press ?)"
        >
          <Icon name="info" size="sm" />
        </button>
      </div>

      {/* Main content area */}
      <div className="pos-main">
        {/* Menu section */}
        <div className="menu-section">
          {/* Search and category filter */}
          <div className="menu-controls">
            <div className="search-bar">
              <Icon name="search" size="sm" />
              <input
                type="text"
                placeholder="Search menu items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
              {searchQuery && (
                <button
                  className="clear-search"
                  onClick={() => setSearchQuery('')}
                  type="button"
                >
                  ×
                </button>
              )}
            </div>
            
            <div className="category-filter">
              {categories.map(category => (
                <button
                  key={category}
                  className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category || 'all')}
                >
                  {category === 'all' ? 'All Items' : (category || 'Other')}
                </button>
              ))}
            </div>
          </div>

          {/* Virtualized menu items grid */}
          <div className="menu-grid-container">
            <VirtualizedMenuGrid
              items={filteredMenuItems}
              onItemClick={addToOrder}
              itemHeight={200}
              itemWidth={180}
              gap={16}
            />
          </div>
        </div>

        {/* Order section */}
        <div className="order-section">
          <div className="order-header">
            <div className="order-title-section">
              <h3>Current Order</h3>
              {currentOrder.length > 0 && (
                <span className="item-count">
                  {currentOrder.reduce((sum, item) => sum + item.quantity, 0)} items
                </span>
              )}
            </div>
            <div className="order-actions-header">
              {currentOrder.length > 0 && (
                <>
                  <button className="quick-btn duplicate-btn" onClick={duplicateLastItem} title="Duplicate last item (Ctrl+D)">
                    <Icon name="plus" size="sm" />
                  </button>
                  <button className="quick-btn clear-btn" onClick={clearOrder} title="Clear order (Ctrl+C)">
                    <Icon name="trash" size="sm" />
                  </button>
                </>
              )}
            </div>
          </div>

          <div className="order-items">
            {currentOrder.length === 0 ? (
              <div className="empty-order">
                <Icon name="shopping-cart" size="xl" />
                <p>No items added</p>
                <p className="empty-subtitle">Tap menu items to add them</p>
              </div>
            ) : (
              currentOrder.map((item, index) => (
                <div key={item.id} className="order-item">
                  <div className="item-index">{index + 1}</div>
                  <div className="item-details">
                    <span className="item-name">{item.menuItemName}</span>
                    <span className="item-price">₹{item.menuItemPrice} each</span>
                  </div>
                  <div className="quantity-controls">
                    <button
                      className="qty-btn decrease"
                      onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      title="Decrease quantity"
                    >
                      -
                    </button>
                    <span className="quantity">{item.quantity}</span>
                    <button
                      className="qty-btn increase"
                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      title="Increase quantity"
                    >
                      +
                    </button>
                  </div>
                  <div className="item-total">₹{item.subtotal.toFixed(2)}</div>
                  <button
                    className="remove-item-btn"
                    onClick={() => updateQuantity(item.id, 0)}
                    title="Remove item"
                  >
                    <Icon name="cancel" size="sm" />
                  </button>
                </div>
              ))
            )}
          </div>

          {/* Order summary and actions */}
          {currentOrder.length > 0 && (
            <div className="order-summary">
              <div className="summary-line">
                <span>Subtotal:</span>
                <span>₹{orderTotals.subtotal.toFixed(2)}</span>
              </div>
              {orderTotals.taxBreakdown.map((tax, index) => (
                <div key={index} className="summary-line">
                  <span>{tax.name} ({tax.type === 'percentage' ? `${tax.rate}%` : `₹${tax.rate}`}):</span>
                  <span>₹{tax.amount.toFixed(2)}</span>
                </div>
              ))}
              {orderTotals.tax === 0 && (
                <div className="summary-line">
                  <span>Tax:</span>
                  <span>₹0.00</span>
                </div>
              )}
              <div className="summary-line total">
                <span>Total:</span>
                <span>₹{orderTotals.total.toFixed(2)}</span>
              </div>

              <button className="create-order-btn" onClick={createOrder}>
                <Icon name="check-circle" size="sm" />
                Create Order
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Visual Table Selector Modal */}
      {showTableSelector && (
        <VisualTableSelector
          tables={tables}
          selectedTable={selectedTable}
          onTableSelect={(table) => {
            setSelectedTable(table);
            setShowTableSelector(false);
          }}
          onClose={() => setShowTableSelector(false)}
        />
      )}

      {/* Keyboard Shortcuts Help Modal */}
      {showKeyboardHelp && (
        <KeyboardShortcutsHelp onClose={() => setShowKeyboardHelp(false)} />
      )}
    </div>
  );
};

export default SimplifiedPOS;
