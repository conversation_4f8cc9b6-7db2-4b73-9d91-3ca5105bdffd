import React, { useState, useEffect } from 'react';
import { MenuItem, TaxRate } from '../types';
import Icon from './Icon';
import { calculateTaxes, formatTaxBreakdown } from '../utils/taxCalculations';

interface OrderItem extends MenuItem {
  quantity: number;
  subtotal: number;
}

interface OrderDisplayProps {
  orderItems: OrderItem[];
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onClearOrder: () => void;
  onProcessPayment: () => void;
  restaurantId: string;
}

const OrderDisplay: React.FC<OrderDisplayProps> = ({
  orderItems,
  onUpdateQuantity,
  onRemoveItem,
  onClearOrder,
  onProcessPayment,
  restaurantId
}) => {
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);

  useEffect(() => {
    loadTaxRates();
  }, [restaurantId]);

  const loadTaxRates = async () => {
    try {
      const data = await window.electronAPI.getTaxRates(restaurantId);
      setTaxRates(data.filter(tax => tax.isActive));
    } catch (error) {
      console.error('Failed to load tax rates:', error);
    }
  };
  const calculateTotal = () => {
    return orderItems.reduce((total, item) => total + item.subtotal, 0);
  };

  const calculateTax = (subtotal: number) => {
    const taxCalculation = calculateTaxes(subtotal, taxRates);
    return taxCalculation;
  };

  const subtotal = calculateTotal();
  const taxCalculation = calculateTax(subtotal);
  const total = taxCalculation.totalAmount;

  if (orderItems.length === 0) {
    return (
      <div className="order-section">
        <h3 className="section-title">Current Order</h3>
        <div className="order-items">
          <div className="empty-order">
            <span className="empty-icon">
              <Icon name="shopping-cart" size="xl" />
            </span>
            <p>No items in order</p>
            <p className="empty-subtitle">Select items from the menu to start</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="order-section">
      <div className="order-header">
        <h3 className="section-title">Current Order</h3>
        <button className="clear-order-btn" onClick={onClearOrder}>
          Clear All
        </button>
      </div>
      
      <div className="order-items">
        {orderItems.map((item) => (
          <div key={item.id} className="order-item">
            <div className="order-item-details">
              <h4 className="order-item-name">{item.name}</h4>
              <p className="order-item-price">₹{item.price.toFixed(2)} each</p>
            </div>
            
            <div className="order-item-controls">
              <div className="quantity-controls">
                <button 
                  className="quantity-btn"
                  onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
                  disabled={item.quantity <= 1}
                >
                  -
                </button>
                <span className="quantity-display">{item.quantity}</span>
                <button 
                  className="quantity-btn"
                  onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                >
                  +
                </button>
              </div>
              
              <div className="order-item-subtotal">
                ₹{item.subtotal.toFixed(2)}
              </div>
              
              <button 
                className="remove-item-btn"
                onClick={() => onRemoveItem(item.id)}
              >
                ×
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="order-summary">
        <div className="summary-line">
          <span>Subtotal:</span>
          <span>₹{subtotal.toFixed(2)}</span>
        </div>
        {taxCalculation.taxBreakdown.map((tax, index) => (
          <div key={index} className="summary-line">
            <span>{tax.name} ({tax.type === 'percentage' ? `${tax.rate}%` : `₹${tax.rate}`}):</span>
            <span>₹{tax.amount.toFixed(2)}</span>
          </div>
        ))}
        {taxCalculation.taxAmount === 0 && (
          <div className="summary-line">
            <span>Tax:</span>
            <span>₹0.00</span>
          </div>
        )}
        <div className="summary-line total-line">
          <span>Total:</span>
          <span>₹{total.toFixed(2)}</span>
        </div>
      </div>

      <div className="order-actions">
        <button 
          className="checkout-btn"
          onClick={onProcessPayment}
          disabled={orderItems.length === 0}
        >
          Process Payment (₹{total.toFixed(2)})
        </button>
      </div>
    </div>
  );
};

export default OrderDisplay;
