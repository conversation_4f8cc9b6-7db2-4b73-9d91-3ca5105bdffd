import React, { useState, useEffect } from 'react';
import { Order, OrderItem, MenuItem, TaxRate } from '../types';
import { useMenu } from '../contexts/MenuContext';
import { calculateDefaultTaxAmount } from '../utils/taxCalculations';
import Icon from './Icon';

interface EditOrderModalProps {
  order: Order;
  onClose: () => void;
  onOrderUpdated: (updatedOrder: Order) => void;
  restaurantId: string;
}

const EditOrderModal: React.FC<EditOrderModalProps> = ({
  order,
  onClose,
  onOrderUpdated,
  restaurantId
}) => {
  const { menuItems } = useMenu();
  const [editedItems, setEditedItems] = useState<OrderItem[]>(order.items);
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddItems, setShowAddItems] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadTaxRates();
  }, [restaurantId]);

  const loadTaxRates = async () => {
    try {
      const data = await window.electronAPI.getTaxRates(restaurantId);
      setTaxRates(data.filter(tax => tax.isActive));
    } catch (error) {
      console.error('Failed to load tax rates:', error);
    }
  };

  const calculateSubtotal = () => {
    return editedItems.reduce((total, item) => total + item.subtotal, 0);
  };

  const calculateTaxAmount = () => {
    const subtotal = calculateSubtotal();
    return calculateDefaultTaxAmount(subtotal, taxRates);
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTaxAmount();
  };

  const updateItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
      return;
    }

    setEditedItems(items =>
      items.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity, subtotal: newQuantity * item.menuItemPrice }
          : item
      )
    );
  };

  const removeItem = (itemId: string) => {
    setEditedItems(items => items.filter(item => item.id !== itemId));
  };

  const addMenuItem = (menuItem: MenuItem) => {
    const existingItem = editedItems.find(item => item.menuItemId === menuItem.id);
    
    if (existingItem) {
      updateItemQuantity(existingItem.id, existingItem.quantity + 1);
    } else {
      const newOrderItem: OrderItem = {
        id: `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        menuItemId: menuItem.id,
        menuItemName: menuItem.name,
        menuItemPrice: menuItem.price,
        quantity: 1,
        subtotal: menuItem.price,
        status: 'pending'
      };
      setEditedItems(items => [...items, newOrderItem]);
    }
  };

  const filteredMenuItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (item.description || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (item.code || '').toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch && item.available;
  });

  const saveChanges = async () => {
    setIsLoading(true);
    try {
      const updatedOrderData = {
        items: editedItems,
        subtotal: calculateSubtotal(),
        taxAmount: calculateTaxAmount(),
        totalAmount: calculateTotal()
      };

      const result = await (window.electronAPI as any).updateOrderItems(order.id, updatedOrderData);
      
      if (result.success) {
        onOrderUpdated(result.order);
        onClose();
      } else {
        alert('Failed to update order: ' + result.error);
      }
    } catch (error) {
      console.error('Error updating order:', error);
      alert('Failed to update order');
    } finally {
      setIsLoading(false);
    }
  };

  const hasChanges = () => {
    if (editedItems.length !== order.items.length) return true;
    
    return editedItems.some(editedItem => {
      const originalItem = order.items.find(item => item.id === editedItem.id);
      return !originalItem || originalItem.quantity !== editedItem.quantity;
    });
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content edit-order-modal">
        <div className="modal-header">
          <h2>Edit Order - {order.orderNumber}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="edit-order-content">
          <div className="current-items-section">
            <h3>Current Items</h3>
            <div className="order-items-list">
              {editedItems.map(item => (
                <div key={item.id} className="order-item-edit">
                  <div className="item-info">
                    <span className="item-name">{item.menuItemName}</span>
                    <span className="item-price">₹{item.menuItemPrice.toFixed(2)}</span>
                  </div>
                  <div className="item-controls">
                    <div className="quantity-controls">
                      <button
                        className="qty-btn"
                        onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                      >
                        -
                      </button>
                      <span className="quantity">{item.quantity}</span>
                      <button
                        className="qty-btn"
                        onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                      >
                        +
                      </button>
                    </div>
                    <span className="item-subtotal">₹{item.subtotal.toFixed(2)}</span>
                    <button
                      className="remove-btn"
                      onClick={() => removeItem(item.id)}
                      title="Remove item"
                    >
                      <Icon name="trash" size="sm" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {showAddItems && (
            <div className="add-items-section">
              <h3>Add Items</h3>
              <div className="search-container">
                <input
                  type="text"
                  placeholder="Search menu items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="search-input"
                />
              </div>
              <div className="menu-items-grid">
                {filteredMenuItems.slice(0, 12).map(item => (
                  <div
                    key={item.id}
                    className="menu-item-card"
                    onClick={() => addMenuItem(item)}
                  >
                    <div className="item-name">{item.name}</div>
                    <div className="item-price">₹{item.price.toFixed(2)}</div>
                    {item.code && <div className="item-code">Code: {item.code}</div>}
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="order-summary">
            <div className="summary-row">
              <span>Subtotal:</span>
              <span>₹{calculateSubtotal().toFixed(2)}</span>
            </div>
            <div className="summary-row">
              <span>Tax:</span>
              <span>₹{calculateTaxAmount().toFixed(2)}</span>
            </div>
            <div className="summary-row total">
              <span>Total:</span>
              <span>₹{calculateTotal().toFixed(2)}</span>
            </div>
          </div>
        </div>

        <div className="modal-actions">
          <button
            className="btn btn-secondary"
            onClick={() => setShowAddItems(!showAddItems)}
          >
            {showAddItems ? 'Hide Menu' : 'Add Items'}
          </button>
          <button className="btn btn-secondary" onClick={onClose}>
            Cancel
          </button>
          <button
            className="btn btn-primary"
            onClick={saveChanges}
            disabled={!hasChanges() || isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditOrderModal;
