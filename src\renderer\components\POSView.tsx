import React from 'react';
import { useMenu } from '../contexts/MenuContext';
import MenuItemCard from './MenuItemCard';
import OrderDisplay from './OrderDisplay';
import ImageResolver from './ImageResolver';
import { MenuItem } from '../types';
import Icon from './Icon';

interface POSViewProps {
  orderItems: any[];
  onAddToOrder: (item: MenuItem) => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onClearOrder: () => void;
  onProcessPayment: () => void;
  restaurantId: string;
}

const POSView: React.FC<POSViewProps> = ({
  orderItems,
  onAddToOrder,
  onUpdateQuantity,
  onRemoveItem,
  onClearOrder,
  onProcessPayment,
  restaurantId
}) => {
  const { menuItems, isLoading } = useMenu();

  if (isLoading) {
    return (
      <div className="pos-loading">
        <div className="loading-spinner"></div>
        <p>Loading menu items...</p>
      </div>
    );
  }

  return (
    <div className="pos-grid">
      <div className="menu-section">
        <h3 className="section-title">Menu Items</h3>
        <div className="menu-grid">
          {menuItems.length === 0 ? (
            <div className="empty-menu-pos">
              <span className="empty-icon">
                <Icon name="utensils" size="xl" />
              </span>
              <h4>No menu items available</h4>
              <p>Add menu items in Menu Management to start taking orders</p>
            </div>
          ) : (
            menuItems
              .filter(item => item.available)
              .map(item => (
                <div key={item.id} className="menu-item-pos">
                  {item.image && (
                    <div className="menu-item-image">
                      <ImageResolver
                        src={item.image}
                        alt={item.name}
                        className="menu-image"
                      />
                    </div>
                  )}
                  <div className="menu-item-content">
                    <div className="menu-item-header">
                      <h4 className="menu-item-name">{item.name}</h4>
                      <span className="menu-item-price">₹{item.price.toFixed(2)}</span>
                    </div>
                    {item.description && (
                      <p className="menu-item-description">{item.description}</p>
                    )}
                    {item.category && (
                      <span className="menu-item-category">{item.category}</span>
                    )}
                    <button
                      className="add-to-order-btn"
                      onClick={() => onAddToOrder(item)}
                    >
                      <Icon name="plus" size="sm" /> Add to Order
                    </button>
                  </div>
                </div>
              ))
          )}
        </div>
      </div>

      <OrderDisplay
        orderItems={orderItems}
        onUpdateQuantity={onUpdateQuantity}
        onRemoveItem={onRemoveItem}
        onClearOrder={onClearOrder}
        onProcessPayment={onProcessPayment}
        restaurantId={restaurantId}
      />
    </div>
  );
};

export default POSView;
