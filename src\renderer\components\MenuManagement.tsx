import React, { useState } from 'react';
import { MenuItem } from '../types';
import CSVImportModal from './CSVImportModal';
import ImageUpload from './ImageUpload';
import ImageResolver from './ImageResolver';
import { useMenu } from '../contexts/MenuContext';
import Icon from './Icon';

interface MenuManagementProps {
  restaurantId?: string;
}

interface MenuItemForm {
  name: string;
  code: string;
  price: string;
  description: string;
  category: string;
  image: string;
  available: boolean;
}

const MenuManagement: React.FC<MenuManagementProps> = ({ restaurantId: propRestaurantId }) => {
  const { menuItems, isLoading, restaurantId, addMenuItem, updateMenuItem, deleteMenuItem, bulkImportMenuItems } = useMenu();
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [formData, setFormData] = useState<MenuItemForm>({
    name: '',
    code: '',
    price: '',
    description: '',
    category: '',
    image: '',
    available: true
  });

  // View and filtering states
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  const categories = [
    'Appetizers',
    'Main Course',
    'Pizza',
    'Pasta',
    'Salads',
    'Desserts',
    'Beverages',
    'Snacks',
    'Specials'
  ];

  // Computed values for filtering and pagination
  const filteredMenuItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.code || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;

    return matchesSearch && matchesCategory;
  });

  const totalPages = Math.ceil(filteredMenuItems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedMenuItems = filteredMenuItems.slice(startIndex, startIndex + itemsPerPage);

  // Get unique categories from menu items
  const uniqueCategories = [...new Set(menuItems.map(item => item.category).filter(Boolean))];



  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      price: '',
      description: '',
      category: '',
      image: '',
      available: true
    });
    setEditingItem(null);
    setShowAddForm(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.price) {
      alert('Please fill in required fields');
      return;
    }

    const price = parseFloat(formData.price);
    if (isNaN(price) || price <= 0) {
      alert('Please enter a valid price');
      return;
    }

    try {
      const menuItemData = {
        name: formData.name,
        code: formData.code.trim() || undefined,
        price,
        description: formData.description,
        category: formData.category,
        image: formData.image,
        available: formData.available,
        restaurantId: 'current' // This will be set by the context
      };

      let success = false;
      if (editingItem) {
        success = await updateMenuItem(editingItem.id, menuItemData);
        if (!success) {
          alert('Failed to update menu item');
        }
      } else {
        success = await addMenuItem(menuItemData);
        if (!success) {
          alert('Failed to create menu item');
        }
      }

      if (success) {
        resetForm();
      }
    } catch (error) {
      console.error('Error saving menu item:', error);
      alert('Failed to save menu item');
    }
  };

  const handleEdit = (item: MenuItem) => {
    setFormData({
      name: item.name,
      code: item.code || '',
      price: item.price.toString(),
      description: item.description || '',
      category: item.category || '',
      image: item.image || '',
      available: item.available
    });
    setEditingItem(item);
    setShowAddForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this menu item?')) {
      return;
    }

    const success = await deleteMenuItem(id);
    if (!success) {
      alert('Failed to delete menu item');
    }
  };

  const toggleAvailability = async (item: MenuItem) => {
    const success = await updateMenuItem(item.id, {
      available: !item.available
    });
    if (!success) {
      alert('Failed to update availability');
    }
  };

  const handleCSVImport = async (items: any[]) => {
    const targetRestaurantId = restaurantId || propRestaurantId;
    if (!targetRestaurantId) {
      alert('Restaurant ID not found. Please try again.');
      return;
    }

    const success = await bulkImportMenuItems(items, targetRestaurantId);
    if (success) {
      alert(`Successfully imported ${items.length} menu items!`);
    } else {
      alert('Failed to import menu items');
    }
  };

  if (isLoading) {
    return (
      <div className="menu-management-loading">
        <div className="loading-spinner"></div>
        <p>Loading menu items...</p>
      </div>
    );
  }

  return (
    <div className="menu-management">
      <div className="menu-management-header">
        <h2>Menu Management</h2>
        <div className="header-actions">
          <button
            className="btn btn-secondary"
            onClick={() => setShowImportModal(true)}
          >
            <Icon name="upload" size="xs" /> Import CSV
          </button>
          <button
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            <Icon name="plus" size="xs" /> Add Menu Item
          </button>
        </div>
      </div>

      {showAddForm && (
        <div className="menu-form-modal">
          <div className="menu-form-overlay" onClick={resetForm}></div>
          <div className="menu-form-container">
            <div className="menu-form-header">
              <h3>{editingItem ? 'Edit Menu Item' : 'Add New Menu Item'}</h3>
              <button className="close-btn" onClick={resetForm}>×</button>
            </div>
            
            <form onSubmit={handleSubmit} className="menu-form">
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Name *</label>
                  <input
                    type="text"
                    className="form-input"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Code</label>
                  <input
                    type="text"
                    className="form-input"
                    placeholder="Item code for quick search"
                    value={formData.code}
                    onChange={(e) => setFormData({...formData, code: e.target.value})}
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Price (₹) *</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    className="form-input"
                    value={formData.price}
                    onChange={(e) => setFormData({...formData, price: e.target.value})}
                    required
                  />
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Description</label>
                <textarea
                  className="form-input"
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Category</label>
                  <select
                    className="form-select"
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                  >
                    <option value="">Select Category</option>
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">
                    <input
                      type="checkbox"
                      checked={formData.available}
                      onChange={(e) => setFormData({...formData, available: e.target.checked})}
                    />
                    Available
                  </label>
                </div>
              </div>

              <ImageUpload
                currentImage={formData.image}
                onImageChange={(imageUrl) => setFormData({...formData, image: imageUrl})}
                onImageRemove={() => setFormData({...formData, image: ''})}
              />

              <div className="form-actions">
                <button type="button" className="btn btn-secondary" onClick={resetForm}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingItem ? 'Update' : 'Create'} Menu Item
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Search and Filter Controls */}
      <div className="menu-controls">
        <div className="search-section">
          <div className="search-input-container">
            <input
              type="text"
              className="search-input"
              placeholder="Search menu items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <span className="search-icon">
              <Icon name="search" size="sm" />
            </span>
          </div>
        </div>

        <div className="filter-section">
          <div className="filter-group">
            <label className="filter-label">Category:</label>
            <select
              className="filter-select"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="all">All Categories</option>
              {uniqueCategories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label className="filter-label">View:</label>
            <div className="view-toggle">
              <button
                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                onClick={() => setViewMode('grid')}
                title="Grid View"
              >
                <Icon name="grid" size="sm" />
              </button>
              <button
                className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
                onClick={() => setViewMode('list')}
                title="List View"
              >
                <Icon name="list" size="sm" />
              </button>
            </div>
          </div>

          <div className="filter-group">
            <label className="filter-label">Per Page:</label>
            <select
              className="filter-select"
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
            >
              <option value={8}>8</option>
              <option value={12}>12</option>
              <option value={24}>24</option>
              <option value={48}>48</option>
            </select>
          </div>
        </div>

        <div className="menu-info">
          <span className="results-count">
            Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredMenuItems.length)} of {filteredMenuItems.length} items
          </span>
          {totalPages > 1 && (
            <div className="pagination">
              <button
                className="page-btn"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <Icon name="chevron-left" size="sm" /> Prev
              </button>
              <span className="page-info">
                Page {currentPage} of {totalPages}
              </span>
              <button
                className="page-btn"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next <Icon name="chevron-right" size="sm" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className={`menu-items-grid ${viewMode}`}>
        {menuItems.length === 0 ? (
          <div className="empty-menu">
            <span className="empty-icon">
              <Icon name="utensils" size="xl" />
            </span>
            <h3>No menu items yet</h3>
            <p>Add your first menu item to get started</p>
            <button
              className="btn btn-primary"
              onClick={() => setShowAddForm(true)}
            >
              Add Menu Item
            </button>
          </div>
        ) : filteredMenuItems.length === 0 ? (
          <div className="empty-menu">
            <span className="empty-icon">
              <Icon name="search" size="xl" />
            </span>
            <h3>No items found</h3>
            <p>Try adjusting your search or filter criteria</p>
            <button
              className="btn btn-secondary"
              onClick={() => {
                setSearchTerm('');
                setCategoryFilter('all');
              }}
            >
              Clear Filters
            </button>
          </div>
        ) : (
          paginatedMenuItems.map(item => (
            <div key={item.id} className={`menu-item-card ${viewMode} ${!item.available ? 'unavailable' : ''}`}>
              {item.image && (
                <div className="menu-item-image">
                  <ImageResolver src={item.image} alt={item.name} />
                </div>
              )}
              <div className="menu-item-content">
                <div className="menu-item-header">
                  <h4 className="menu-item-name">{item.name}</h4>
                  <span className="menu-item-price">₹{item.price.toFixed(2)}</span>
                </div>
                {item.code && (
                  <div className="menu-item-code">Code: {item.code}</div>
                )}
                {item.description && (
                  <p className="menu-item-description">{item.description}</p>
                )}
                {item.category && (
                  <span className="menu-item-category">{item.category}</span>
                )}
                <div className="menu-item-actions">
                  <button
                    className={`availability-btn ${item.available ? 'available' : 'unavailable'}`}
                    onClick={() => toggleAvailability(item)}
                  >
                    <Icon name={item.available ? 'check-circle' : 'x-circle'} size="xs" />
                    {item.available ? ' Available' : ' Unavailable'}
                  </button>
                  <button
                    className="edit-btn"
                    onClick={() => handleEdit(item)}
                  >
                    <Icon name="edit" size="xs" /> Edit
                  </button>
                  <button
                    className="delete-btn"
                    onClick={() => handleDelete(item.id)}
                  >
                    <Icon name="trash" size="xs" /> Delete
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <CSVImportModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImport={handleCSVImport}
      />
    </div>
  );
};

export default MenuManagement;
