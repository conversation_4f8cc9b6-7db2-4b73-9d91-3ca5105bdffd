import React, { useState, useEffect } from 'react';
import { Order, BillingSettings, TaxRate } from '../types';
import { eventBus, EVENTS } from '../utils/eventBus';
import { calculateTaxes, formatTaxBreakdown } from '../utils/taxCalculations';

interface BillingSystemProps {
  order: Order;
  onClose: () => void;
  onPaymentComplete: (paymentData: PaymentData) => void;
}

interface PaymentData {
  method: 'cash' | 'card' | 'upi' | 'other';
  amountReceived: number;
  changeGiven: number;
  transactionId?: string;
  notes?: string;
}

const BillingSystem: React.FC<BillingSystemProps> = ({ order, onClose, onPaymentComplete }) => {
  const [billingSettings, setBillingSettings] = useState<BillingSettings | null>(null);
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<PaymentData['method']>('cash');
  const [amountReceived, setAmountReceived] = useState<string>(order.totalAmount.toString());
  const [transactionId, setTransactionId] = useState('');
  const [notes, setNotes] = useState('');
  const [showReceipt, setShowReceipt] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    loadBillingData();
  }, [order.restaurantId]);

  const loadBillingData = async () => {
    try {
      const [billingData, taxData] = await Promise.all([
        (window.electronAPI as any).getBillingSettings(order.restaurantId),
        (window.electronAPI as any).getTaxRates(order.restaurantId)
      ]);

      console.log('Loaded billing data:', billingData);
      console.log('Loaded tax data:', taxData);

      if (billingData) {
        setBillingSettings(billingData);
      } else {
        // Create default billing settings if none exist
        const defaultSettings: BillingSettings = {
          id: '',
          restaurantId: order.restaurantId,
          header: {
            showLogo: false,
            restaurantName: 'Restaurant',
            address: '',
            phone: '',
            email: '',
            website: '',
            gstNumber: '',
            customText: ''
          },
          footer: {
            thankYouMessage: 'Thank you for your visit!',
            termsAndConditions: '',
            customText: '',
            showQRCode: false,
            qrCodeData: ''
          },
          format: {
            paperSize: 'thermal_80mm',
            fontSize: 'medium',
            showItemImages: false,
            showTaxBreakdown: true
          },
          printer: {
            printerName: '',
            autoprint: false,
            copies: 1
          },
          updatedAt: new Date().toISOString()
        };
        setBillingSettings(defaultSettings);
      }

      setTaxRates(taxData.filter((tax: TaxRate) => tax.isActive));
    } catch (error) {
      console.error('Failed to load billing data:', error);
    }
  };

  const calculateChange = () => {
    const received = parseFloat(amountReceived) || 0;
    return Math.max(0, received - order.totalAmount);
  };

  const isPaymentValid = () => {
    const received = parseFloat(amountReceived) || 0;
    if (paymentMethod === 'cash') {
      return received >= order.totalAmount;
    }
    // For card/UPI, allow exact amount or close to it (within 1 rupee difference)
    return Math.abs(received - order.totalAmount) <= 1;
  };

  const handlePaymentMethodChange = (method: PaymentData['method']) => {
    setPaymentMethod(method);
    // Auto-set amount based on payment method
    if (method === 'cash') {
      // For cash, keep current amount or set to total if empty
      if (!amountReceived || parseFloat(amountReceived) < order.totalAmount) {
        setAmountReceived(Math.ceil(order.totalAmount).toString());
      }
    } else {
      // For card/UPI, set exact amount
      setAmountReceived(order.totalAmount.toFixed(2));
    }
  };

  const handleAmountChange = (value: string) => {
    // Allow only numbers and decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '');
    // Prevent multiple decimal points
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      return;
    }
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return;
    }
    setAmountReceived(cleanValue);
  };

  const handlePayment = async () => {
    if (!isPaymentValid()) {
      alert('Please enter a valid payment amount');
      return;
    }

    setIsProcessing(true);

    try {
      const paymentData: PaymentData = {
        method: paymentMethod,
        amountReceived: parseFloat(amountReceived),
        changeGiven: calculateChange(),
        transactionId: transactionId || undefined,
        notes: notes || undefined
      };

      // Update order payment status
      await (window.electronAPI as any).updateOrder(order.id, {
        paymentStatus: 'paid',
        paymentMethod: paymentMethod,
        billPrinted: true
      });

      // Emit events for dashboard refresh
      eventBus.emit(EVENTS.PAYMENT_COMPLETED, { order, paymentData });
      eventBus.emit(EVENTS.ORDER_UPDATED, order);
      eventBus.emit(EVENTS.DASHBOARD_REFRESH);

      onPaymentComplete(paymentData);
      setShowReceipt(true);
    } catch (error) {
      console.error('Payment processing failed:', error);
      alert('Payment processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const generateReceiptHTML = () => {
    const timestamp = new Date().toLocaleString('en-IN');
    const settings = billingSettings;

    console.log('Generating receipt with settings:', settings);

    return `
      <div class="receipt">
        <div class="receipt-header">
          ${settings?.header.showLogo && settings.header.logoUrl ?
            `<img src="${settings.header.logoUrl}" alt="Logo" class="receipt-logo" />` : ''}
          <h1 class="restaurant-name">${settings?.header.restaurantName || 'Restaurant'}</h1>
          ${settings?.header.address ? `<p class="address">${settings.header.address}</p>` : ''}
          <div class="contact-info">
            ${settings?.header.phone ? `<p>Phone: ${settings.header.phone}</p>` : ''}
            ${settings?.header.email ? `<p>Email: ${settings.header.email}</p>` : ''}
            ${settings?.header.website ? `<p>Website: ${settings.header.website}</p>` : ''}
            ${settings?.header.gstNumber ? `<p>GST: ${settings.header.gstNumber}</p>` : ''}
            ${settings?.header.customText ? `<p class="custom-header">${settings.header.customText}</p>` : ''}
          </div>
        </div>

        <div class="receipt-divider">
          <h2>BILL RECEIPT</h2>
        </div>

        <div class="order-details">
          <div class="detail-row"><span>Order No:</span><span>${order.orderNumber}</span></div>
          <div class="detail-row"><span>Date:</span><span>${timestamp}</span></div>
          <div class="detail-row"><span>Type:</span><span>${order.orderType.toUpperCase()}</span></div>
          ${order.tableNumber ? `<div class="detail-row"><span>Table:</span><span>${order.tableNumber}</span></div>` : ''}
          ${order.customerName ? `<div class="detail-row"><span>Customer:</span><span>${order.customerName}</span></div>` : ''}
          ${order.customerPhone ? `<div class="detail-row"><span>Phone:</span><span>${order.customerPhone}</span></div>` : ''}
        </div>

        <div class="items-section">
          <h3>ITEMS</h3>
          <div class="items-list">
            ${order.items.map(item => `
              <div class="item">
                <div class="item-name">${item.menuItemName}</div>
                <div class="item-line">
                  <span>${item.quantity} x ₹${item.menuItemPrice.toFixed(2)}</span>
                  <span>₹${item.subtotal.toFixed(2)}</span>
                </div>
                ${item.notes ? `<div class="item-note">Note: ${item.notes}</div>` : ''}
              </div>
            `).join('')}
          </div>
        </div>

        <div class="totals-section">
          <div class="total-row"><span>Subtotal:</span><span>₹${order.subtotal.toFixed(2)}</span></div>
          ${settings?.format.showTaxBreakdown && taxRates.length > 0 ?
            taxRates.filter(tax => tax.isActive).map(tax => {
              const taxAmount = tax.type === 'percentage'
                ? (order.subtotal * tax.rate) / 100
                : tax.rate;
              return `<div class="total-row"><span>${tax.name}:</span><span>₹${taxAmount.toFixed(2)}</span></div>`;
            }).join('') :
            `<div class="total-row"><span>Tax:</span><span>₹${order.taxAmount.toFixed(2)}</span></div>`
          }
          <div class="total-row grand-total">
            <span>TOTAL:</span><span>₹${order.totalAmount.toFixed(2)}</span>
          </div>
        </div>

        <div class="payment-section">
          <h3>PAYMENT DETAILS</h3>
          <div class="payment-row"><span>Method:</span><span>${paymentMethod.toUpperCase()}</span></div>
          <div class="payment-row"><span>Amount Received:</span><span>₹${parseFloat(amountReceived).toFixed(2)}</span></div>
          ${paymentMethod === 'cash' && calculateChange() > 0 ?
            `<div class="payment-row"><span>Change Given:</span><span>₹${calculateChange().toFixed(2)}</span></div>` : ''}
          ${transactionId ? `<div class="payment-row"><span>Transaction ID:</span><span>${transactionId}</span></div>` : ''}
        </div>

        <div class="receipt-footer">
          ${settings?.footer.thankYouMessage ? `<p class="thank-you">${settings.footer.thankYouMessage}</p>` :
            '<p class="thank-you">Thank you for your visit!</p>'}
          ${settings?.footer.termsAndConditions ? `<p class="terms">${settings.footer.termsAndConditions}</p>` : ''}
          ${settings?.footer.customText ? `<p class="custom-text">${settings.footer.customText}</p>` : ''}
          ${settings?.footer.showQRCode && settings.footer.qrCodeData ?
            `<div class="qr-code">
              <p>Scan for more info:</p>
              <p class="qr-data">${settings.footer.qrCodeData}</p>
            </div>` : ''}
          <p class="timestamp">${timestamp}</p>
        </div>
      </div>
    `;
  };

  const generateReceiptText = () => {
    const timestamp = new Date().toLocaleString('en-IN');
    const settings = billingSettings;

    let receipt = '';

    // Header
    if (settings?.header.showLogo && settings.header.logoUrl) {
      receipt += `[LOGO: ${settings.header.logoUrl}]\n`;
    }

    receipt += `${settings?.header.restaurantName || 'Restaurant'}\n`;
    if (settings?.header.address) receipt += `${settings.header.address}\n`;
    if (settings?.header.phone) receipt += `Phone: ${settings.header.phone}\n`;
    if (settings?.header.email) receipt += `Email: ${settings.header.email}\n`;
    if (settings?.header.website) receipt += `Website: ${settings.header.website}\n`;
    if (settings?.header.gstNumber) receipt += `GST: ${settings.header.gstNumber}\n`;

    receipt += `\n${'='.repeat(40)}\n`;
    receipt += `BILL RECEIPT\n`;
    receipt += `${'='.repeat(40)}\n`;

    // Order Details
    receipt += `Order No: ${order.orderNumber}\n`;
    receipt += `Date: ${timestamp}\n`;
    receipt += `Type: ${order.orderType.toUpperCase()}\n`;
    if (order.tableNumber) receipt += `Table: ${order.tableNumber}\n`;
    if (order.customerName) receipt += `Customer: ${order.customerName}\n`;
    if (order.customerPhone) receipt += `Phone: ${order.customerPhone}\n`;

    receipt += `\n${'-'.repeat(40)}\n`;
    receipt += `ITEMS\n`;
    receipt += `${'-'.repeat(40)}\n`;

    // Items
    order.items.forEach(item => {
      receipt += `${item.menuItemName}\n`;
      receipt += `  ${item.quantity} x ₹${item.menuItemPrice} = ₹${item.subtotal.toFixed(2)}\n`;
      if (item.notes) receipt += `  Note: ${item.notes}\n`;
    });

    receipt += `${'-'.repeat(40)}\n`;

    // Totals
    receipt += `Subtotal: ₹${order.subtotal.toFixed(2)}\n`;

    // Tax breakdown
    if (settings?.format.showTaxBreakdown && taxRates.length > 0) {
      const defaultTax = taxRates.find(tax => tax.isDefault);
      if (defaultTax) {
        const taxAmount = defaultTax.type === 'percentage'
          ? (order.subtotal * defaultTax.rate) / 100
          : defaultTax.rate;
        receipt += `${defaultTax.name}: ₹${taxAmount.toFixed(2)}\n`;
      }
    } else {
      receipt += `Tax: ₹${order.taxAmount.toFixed(2)}\n`;
    }

    receipt += `${'='.repeat(40)}\n`;
    receipt += `TOTAL: ₹${order.totalAmount.toFixed(2)}\n`;
    receipt += `${'='.repeat(40)}\n`;

    // Payment Details
    receipt += `\nPAYMENT DETAILS\n`;
    receipt += `Method: ${paymentMethod.toUpperCase()}\n`;
    receipt += `Amount Received: ₹${parseFloat(amountReceived).toFixed(2)}\n`;
    if (paymentMethod === 'cash' && calculateChange() > 0) {
      receipt += `Change Given: ₹${calculateChange().toFixed(2)}\n`;
    }
    if (transactionId) receipt += `Transaction ID: ${transactionId}\n`;

    // Footer
    receipt += `\n${'-'.repeat(40)}\n`;
    if (settings?.footer.thankYouMessage) {
      receipt += `${settings.footer.thankYouMessage}\n`;
    } else {
      receipt += `Thank you for your visit!\n`;
    }
    if (settings?.footer.termsAndConditions) {
      receipt += `${settings.footer.termsAndConditions}\n`;
    }
    if (settings?.footer.customText) {
      receipt += `${settings.footer.customText}\n`;
    }
    if (settings?.footer.showQRCode && settings.footer.qrCodeData) {
      receipt += `\nScan for more info:\n${settings.footer.qrCodeData}\n`;
    }

    receipt += `\n${timestamp}\n`;

    return receipt;
  };

  const getReceiptStyles = () => `
    <style>
      .receipt {
        max-width: 300px;
        margin: 0 auto;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
      }
      .receipt-header {
        text-align: center;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
        margin-bottom: 10px;
      }
      .receipt-logo {
        max-width: 80px;
        height: auto;
        margin-bottom: 10px;
      }
      .restaurant-name {
        font-size: 16px;
        font-weight: bold;
        margin: 5px 0;
        text-transform: uppercase;
      }
      .address {
        font-size: 11px;
        margin: 2px 0;
      }
      .contact-info p {
        font-size: 10px;
        margin: 1px 0;
      }
      .receipt-divider {
        text-align: center;
        border-bottom: 1px solid #000;
        padding: 5px 0;
        margin: 10px 0;
      }
      .receipt-divider h2 {
        font-size: 14px;
        font-weight: bold;
        margin: 0;
      }
      .order-details {
        margin: 10px 0;
      }
      .detail-row {
        display: flex;
        justify-content: space-between;
        margin: 2px 0;
        font-size: 11px;
      }
      .items-section {
        margin: 15px 0;
      }
      .items-section h3 {
        font-size: 12px;
        font-weight: bold;
        text-align: center;
        border-bottom: 1px solid #000;
        padding-bottom: 5px;
        margin-bottom: 10px;
      }
      .item {
        margin: 8px 0;
        border-bottom: 1px dotted #ccc;
        padding-bottom: 5px;
      }
      .item-name {
        font-weight: bold;
        font-size: 11px;
      }
      .item-line {
        display: flex;
        justify-content: space-between;
        font-size: 10px;
        margin: 2px 0;
      }
      .item-note {
        font-size: 9px;
        font-style: italic;
        color: #666;
        margin: 2px 0;
      }
      .totals-section {
        border-top: 1px solid #000;
        padding-top: 10px;
        margin: 15px 0;
      }
      .total-row {
        display: flex;
        justify-content: space-between;
        margin: 3px 0;
        font-size: 11px;
      }
      .grand-total {
        font-weight: bold;
        font-size: 13px;
        border-top: 1px solid #000;
        padding-top: 5px;
        margin-top: 8px;
      }
      .payment-section {
        border-top: 1px solid #000;
        padding-top: 10px;
        margin: 15px 0;
      }
      .payment-section h3 {
        font-size: 12px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 8px;
      }
      .payment-row {
        display: flex;
        justify-content: space-between;
        margin: 2px 0;
        font-size: 10px;
      }
      .receipt-footer {
        border-top: 1px solid #000;
        padding-top: 10px;
        margin-top: 15px;
        text-align: center;
      }
      .receipt-footer p {
        font-size: 10px;
        margin: 3px 0;
      }
      .thank-you {
        font-weight: bold;
        font-size: 11px;
      }
      .visit-again {
        font-weight: bold;
        margin-top: 10px;
      }
      .timestamp {
        font-size: 9px;
        color: #666;
      }
      @media print {
        .receipt {
          margin: 0;
          max-width: none;
        }
        body {
          margin: 0;
          padding: 10px;
        }
      }
    </style>
  `;

  const printReceipt = () => {
    const receiptHTML = generateReceiptHTML();
    const styles = getReceiptStyles();

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Receipt - ${order.orderNumber}</title>
            ${styles}
          </head>
          <body>
            ${receiptHTML}
          </body>
        </html>
      `);
      printWindow.document.close();

      // Try to print, if it fails, offer PDF download
      try {
        printWindow.print();
      } catch (error) {
        console.error('Print failed:', error);
        generatePDF();
      }
    }
  };

  const generatePDF = () => {
    const receiptHTML = generateReceiptHTML();
    const styles = getReceiptStyles();

    // Create a complete HTML document
    const fullHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt - ${order.orderNumber}</title>
          <meta charset="utf-8">
          ${styles}
        </head>
        <body>
          ${receiptHTML}
        </body>
      </html>
    `;

    // Create a blob and download link
    const blob = new Blob([fullHTML], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Receipt-${order.orderNumber}-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    // Also try to save as PDF using browser's print to PDF
    setTimeout(() => {
      const pdfWindow = window.open('', '_blank');
      if (pdfWindow) {
        pdfWindow.document.write(`
          <html>
            <head>
              <title>Receipt - ${order.orderNumber}</title>
              ${styles}
            </head>
            <body>
              ${receiptHTML}
              <script>
                window.onload = function() {
                  setTimeout(function() {
                    window.print();
                  }, 500);
                };
              </script>
            </body>
          </html>
        `);
        pdfWindow.document.close();
      }
    }, 100);
  };

  if (showReceipt) {
    return (
      <div className="billing-modal">
        <div className="modal-overlay"></div>
        <div className="modal-content receipt-modal">
          <div className="modal-header">
            <h2>Payment Successful!</h2>
            <button className="close-btn" onClick={onClose}>×</button>
          </div>
          
          <div className="receipt-content">
            <div className="receipt-preview">
              <div
                className="receipt-html-preview"
                dangerouslySetInnerHTML={{ __html: generateReceiptHTML() }}
              />
            </div>

            <div className="receipt-actions">
              <button className="btn btn-primary" onClick={printReceipt}>
                🖨️ Print Receipt
              </button>
              <button className="btn btn-secondary" onClick={generatePDF}>
                📄 Save as PDF
              </button>
              <button className="btn btn-secondary" onClick={onClose}>
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="billing-modal">
      <div className="modal-overlay" onClick={onClose}></div>
      <div className="modal-content billing-modal-content">
        <div className="modal-header">
          <h2>Process Payment - {order.orderNumber}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>
        
        <div className="billing-content">
          <div className="receipt-preview-section">
            <h3>Bill Preview</h3>
            <div className="live-receipt-preview">
              <div
                className="receipt-preview-container"
                dangerouslySetInnerHTML={{ __html: generateReceiptHTML() }}
              />
            </div>
          </div>

          <div className="payment-section">
            <h3>Payment Details</h3>
            
            <div className="payment-method-selector">
              <label>Payment Method:</label>
              <div className="payment-methods">
                {[
                  { value: 'cash', label: '💵 Cash', icon: '💵' },
                  { value: 'card', label: '💳 Card', icon: '💳' },
                  { value: 'upi', label: '📱 UPI', icon: '📱' },
                  { value: 'other', label: '🔄 Other', icon: '🔄' }
                ].map(method => (
                  <button
                    key={method.value}
                    className={`payment-method-btn ${paymentMethod === method.value ? 'active' : ''}`}
                    onClick={() => handlePaymentMethodChange(method.value as PaymentData['method'])}
                  >
                    {method.icon} {method.label.split(' ')[1]}
                  </button>
                ))}
              </div>
            </div>

            <div className="payment-inputs">
              <div className="form-group">
                <label>Amount Received *</label>
                <input
                  type="text"
                  inputMode="decimal"
                  value={amountReceived}
                  onChange={(e) => handleAmountChange(e.target.value)}
                  placeholder={paymentMethod === 'cash' ? 'Enter amount received' : order.totalAmount.toFixed(2)}
                  className="form-input amount-input"
                />
              </div>

              {paymentMethod === 'cash' && parseFloat(amountReceived) > 0 && (
                <div className="change-display">
                  <div className="change-amount">
                    Change to Return: <strong>₹{calculateChange().toFixed(2)}</strong>
                  </div>
                </div>
              )}

              {(paymentMethod === 'card' || paymentMethod === 'upi') && (
                <div className="form-group">
                  <label>Transaction ID</label>
                  <input
                    type="text"
                    value={transactionId}
                    onChange={(e) => setTransactionId(e.target.value)}
                    placeholder="Enter transaction ID"
                    className="form-input"
                  />
                </div>
              )}

              <div className="form-group">
                <label>Notes (Optional)</label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Any additional notes..."
                  className="form-input"
                  rows={2}
                />
              </div>
            </div>

            <div className="payment-validation">
              {!isPaymentValid() && parseFloat(amountReceived) > 0 && (
                <div className="validation-error">
                  {paymentMethod === 'cash' 
                    ? 'Amount received must be at least the total amount'
                    : 'Please enter the payment amount'
                  }
                </div>
              )}
            </div>
          </div>

          <div className="billing-actions">
            <button className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button 
              className="btn btn-primary" 
              onClick={handlePayment}
              disabled={!isPaymentValid() || isProcessing}
            >
              {isProcessing ? 'Processing...' : `Process Payment (₹${order.totalAmount.toFixed(2)})`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingSystem;
